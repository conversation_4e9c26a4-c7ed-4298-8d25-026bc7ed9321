// ==UserScript==
// @name         Bilibili评论瀑布流
// @namespace    http://tampermonkey.net/
// @version      2.4.0
// @description  为B站评论区添加瀑布流显示功能，支持视频链接识别和排序
// <AUTHOR>
// @match        https://www.bilibili.com/video/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 配置常量
    const CONFIG = {
        API_BASE: 'https://api.bilibili.com',
        COMMENT_TYPE: 1,
        CACHE_DURATION: 5 * 60 * 1000, // 5分钟缓存
        REQUEST_TIMEOUT: 10000, // 10秒超时
        MAX_RETRIES: 3,
        RETRY_DELAY: 1000
    };

    // 工具类
    class Utils {
        static log(level, message, data = null) {
            const timestamp = new Date().toISOString();
            const prefix = `[Bilibili瀑布流 ${timestamp}]`;

            if (level === 'error') {
                console.error(prefix, message, data || '');
            } else if (level === 'warn') {
                console.warn(prefix, message, data || '');
            } else {
                console.log(prefix, message, data || '');
            }
        }

        static formatNumber(num) {
            if (num >= 10000) {
                return (num / 10000).toFixed(1) + '万';
            }
            return num.toString();
        }

        static formatTime(timestamp) {
            if (!timestamp) return '未知时间';

            const now = Date.now();
            const time = timestamp * 1000;
            const diff = now - time;

            if (diff < 60000) return '刚刚';
            if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
            if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
            if (diff < 2592000000) return Math.floor(diff / 86400000) + '天前';
            if (diff < 31536000000) return Math.floor(diff / 2592000000) + '个月前';
            return Math.floor(diff / 31536000000) + '年前';
        }

        static formatDetailedTime(timestamp) {
            if (!timestamp) return '未知时间';

            const date = new Date(timestamp * 1000);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }

        // 处理评论内容中的视频链接
        static processCommentContent(content) {
            if (!content) return '内容为空';

            // 先转义HTML特殊字符防止XSS
            const escapeHtml = (text) => {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            };

            const escapedContent = escapeHtml(content);

            // 识别av号和BV号的正则表达式
            const avPattern = /\b(av)(\d+)\b/gi;
            const bvPattern = /\b(BV[a-zA-Z0-9]+)\b/gi;

            let processedContent = escapedContent;

            // 处理av号
            processedContent = processedContent.replace(avPattern, (match, _, number) => {
                const url = `https://www.bilibili.com/video/av${number}/`;
                return `<a href="${url}" target="_blank" class="bili-video-link-simple" style="color: #00a1d6; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.2s ease; cursor: pointer; padding: 2px 4px; border-radius: 3px; background: rgba(0,161,214,0.05);">${match}</a>`;
            });

            // 处理BV号
            processedContent = processedContent.replace(bvPattern, (match) => {
                const url = `https://www.bilibili.com/video/${match}/`;
                return `<a href="${url}" target="_blank" class="bili-video-link-simple" style="color: #00a1d6; text-decoration: none; border-bottom: 1px solid transparent; transition: all 0.2s ease; cursor: pointer; padding: 2px 4px; border-radius: 3px; background: rgba(0,161,214,0.05);">${match}</a>`;
            });

            return processedContent;
        }

        // 为视频链接添加悬停效果
        static addVideoLinkHoverEffects(container) {
            const videoLinks = container.querySelectorAll('.bili-video-link-simple');
            videoLinks.forEach(link => {
                link.addEventListener('mouseover', () => {
                    link.style.borderBottomColor = '#00a1d6';
                    link.style.color = '#40a9ff';
                    link.style.background = 'rgba(0,161,214,0.1)';
                });

                link.addEventListener('mouseout', () => {
                    link.style.borderBottomColor = 'transparent';
                    link.style.color = '#00a1d6';
                    link.style.background = 'rgba(0,161,214,0.05)';
                });
            });
        }
    }

    // B站评论API类
    class BilibiliCommentAPI {
        constructor() {
            this.cache = new Map();
        }

        async getAllReplies(oid, rootId, retryCount = 0) {
            const cacheKey = `${oid}_${rootId}`;

            // 检查缓存
            if (this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < CONFIG.CACHE_DURATION) {
                    return cached.data;
                }
            }

            try {
                const url = `${CONFIG.API_BASE}/x/v2/reply/reply?type=${CONFIG.COMMENT_TYPE}&oid=${oid}&root=${rootId}&ps=20&pn=1`;

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), CONFIG.REQUEST_TIMEOUT);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Referer': 'https://www.bilibili.com/'
                    },
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.code !== 0) {
                    throw new Error(`API错误 ${data.code}: ${data.message || '未知错误'}`);
                }

                const replies = data.data?.replies || [];

                // 缓存结果
                this.cache.set(cacheKey, {
                    data: replies,
                    timestamp: Date.now()
                });

                return replies;

            } catch (error) {
                if (error.name === 'AbortError') {
                    throw new Error('请求超时');
                }

                if (retryCount < CONFIG.MAX_RETRIES) {
                    await new Promise(resolve => setTimeout(resolve, CONFIG.RETRY_DELAY * (retryCount + 1)));
                    return this.getAllReplies(oid, rootId, retryCount + 1);
                }

                throw error;
            }
        }

        clearCache() {
            this.cache.clear();
        }
    }

    // DOM监听器类
    class DOMWatcher {
        constructor() {
            this.viewMoreButtons = new Set();
            this.observer = null;
            this.onViewMoreClick = null;
            this.intervalId = null;
            this.scanInterval = 2000; // 降低扫描频率到2秒
            this.attachedButtons = new Map(); // 跟踪已附加的按钮
            this.lastDOMChangeTime = 0; // 记录最后一次DOM变化时间
        }

        start() {
            this.observer = new MutationObserver((mutations) => {
                let shouldRescan = false;

                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        const hasCommentChanges = Array.from(mutation.addedNodes).some(node =>
                            node.nodeType === Node.ELEMENT_NODE &&
                            (node.tagName === 'BILI-COMMENT-THREAD-RENDERER' ||
                             node.querySelector && node.querySelector('bili-comment-thread-renderer'))
                        );

                        if (hasCommentChanges) {
                            shouldRescan = true;
                        }
                    }
                });

                if (shouldRescan) {
                    this.lastDOMChangeTime = Date.now();
                    setTimeout(() => {
                        this.scanForViewMoreButtons();
                        this.reattachMissingButtons();
                    }, 100);
                }
            });

            this.observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            this.startPeriodicScan();
            this.scanForViewMoreButtons();
            Utils.log('info', 'DOM监听器已启动');
        }

        startPeriodicScan() {
            if (this.intervalId) {
                clearInterval(this.intervalId);
            }

            this.intervalId = setInterval(() => {
                this.scanForViewMoreButtons();
                // 只在最近有DOM变化或者距离上次检查超过10秒时才重新附加按钮
                const now = Date.now();
                if (now - this.lastDOMChangeTime < 5000 || now - (this.lastReattachTime || 0) > 10000) {
                    this.reattachMissingButtons();
                    this.lastReattachTime = now;
                }
            }, this.scanInterval);
        }

        stop() {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }

            if (this.intervalId) {
                clearInterval(this.intervalId);
                this.intervalId = null;
            }

            // 清理跟踪数据
            this.attachedButtons.clear();
            this.viewMoreButtons.clear();
        }

        // 查找瀑布流按钮 - 增强版，支持Shadow DOM
        findWaterfallButton(threadRenderer) {
            try {
                // 在多个可能的位置查找瀑布流按钮
                const possibleLocations = [
                    threadRenderer,
                    threadRenderer.shadowRoot?.querySelector("#body"),
                    threadRenderer.shadowRoot?.querySelector("#content"),
                    threadRenderer.shadowRoot?.querySelector("#replies")
                ];

                for (const location of possibleLocations) {
                    if (location) {
                        // 直接查找
                        const waterfallBtn = location.querySelector?.('.bili-waterfall-btn');
                        if (waterfallBtn) {
                            return waterfallBtn;
                        }

                        // 在shadowRoot中查找
                        if (location.shadowRoot) {
                            const waterfallBtn = location.shadowRoot.querySelector('.bili-waterfall-btn');
                            if (waterfallBtn) {
                                return waterfallBtn;
                            }
                        }

                        // 查找包装器
                        const wrapper = location.querySelector?.('.bili-waterfall-wrapper');
                        if (wrapper) {
                            const btn = wrapper.querySelector('.bili-waterfall-btn');
                            if (btn) {
                                return btn;
                            }
                        }
                    }
                }
                return null;
            } catch (error) {
                return null;
            }
        }

        // 生成按钮唯一标识符
        generateButtonId(threadRenderer, commentInfo) {
            // 使用评论信息生成稳定的ID，不包含时间戳以避免重复
            // threadRenderer用于未来可能的扩展
            return `${commentInfo.oid}_${commentInfo.rootId}`;
        }

        scanForViewMoreButtons() {
            try {
                const commentApp = document.querySelector("#commentapp > bili-comments");
                if (!commentApp || !commentApp.shadowRoot) return;

                const threadRenderers = commentApp.shadowRoot.querySelectorAll("#feed > bili-comment-thread-renderer");
                let newButtonsFound = 0;

                threadRenderers.forEach((threadRenderer) => {
                    if (!threadRenderer.shadowRoot) return;

                    const repliesRenderer = threadRenderer.shadowRoot.querySelector("#replies > bili-comment-replies-renderer");
                    if (!repliesRenderer || !repliesRenderer.shadowRoot) return;

                    const viewMoreButton = repliesRenderer.shadowRoot.querySelector("#view-more > bili-text-button");
                    if (!viewMoreButton || !viewMoreButton.shadowRoot) return;

                    const button = viewMoreButton.shadowRoot.querySelector("button");
                    if (!button || this.viewMoreButtons.has(button)) return;

                    this.processViewMoreButton(viewMoreButton, button, threadRenderer);
                    newButtonsFound++;
                });

                if (newButtonsFound > 0) {
                    Utils.log('info', `新处理了 ${newButtonsFound} 个"点击查看"按钮`);
                }

            } catch (error) {
                Utils.log('error', '扫描按钮时出错:', error);
            }
        }

        processViewMoreButton(container, button, threadRenderer) {
            this.viewMoreButtons.add(button);

            const commentInfo = this.extractCommentInfo(container, threadRenderer);
            if (!commentInfo) return;

            this.addWaterfallButton(threadRenderer, commentInfo);
        }

        addWaterfallButton(threadRenderer, commentInfo) {
            // 检查是否已经存在瀑布流按钮
            if (this.findWaterfallButton(threadRenderer)) return;

            // 生成唯一标识符
            const buttonId = this.generateButtonId(threadRenderer, commentInfo);

            // 检查是否已经跟踪了这个按钮
            if (this.attachedButtons.has(buttonId)) return;

            const waterfallBtn = document.createElement('button');
            waterfallBtn.className = 'bili-waterfall-btn';
            waterfallBtn.setAttribute('data-button-id', buttonId);
            waterfallBtn.style.cssText = `
                margin-left: 8px;
                padding: 6px 10px;
                background: linear-gradient(135deg, #00a1d6, #0084b4);
                color: #ffffff;
                border: 1px solid rgba(0, 161, 214, 0.3);
                border-radius: 6px;
                font-size: 12px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 2px 6px rgba(0, 161, 214, 0.2);
                display: inline-flex;
                align-items: center;
                gap: 5px;
                position: relative;
                overflow: hidden;
            `;

            waterfallBtn.innerHTML = `
                <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M4 4h4v4H4V4zm6 0h4v4h-4V4zm6 0h4v4h-4V4zM4 10h4v4H4v-4zm6 0h4v4h-4v-4zm6 0h4v4h-4v-4zM4 16h4v4H4v-4zm6 0h4v4h-4v-4zm6 0h4v4h-4v-4z"/>
                </svg>
                <span>瀑布流</span>
            `;

            // 悬停效果
            waterfallBtn.onmouseover = () => {
                waterfallBtn.style.background = 'linear-gradient(135deg, #40a9ff, #1890ff)';
                waterfallBtn.style.transform = 'translateY(-2px) scale(1.05)';
                waterfallBtn.style.boxShadow = '0 6px 12px rgba(0, 161, 214, 0.4)';
            };

            waterfallBtn.onmouseout = () => {
                waterfallBtn.style.background = 'linear-gradient(135deg, #00a1d6, #0084b4)';
                waterfallBtn.style.transform = 'translateY(0) scale(1)';
                waterfallBtn.style.boxShadow = '0 2px 6px rgba(0, 161, 214, 0.2)';
            };

            // 点击事件
            waterfallBtn.onclick = (e) => {
                e.preventDefault();
                e.stopPropagation();
                if (this.onViewMoreClick) {
                    this.onViewMoreClick(commentInfo);
                }
            };

            // 添加到稳定位置
            const targetContainer = threadRenderer.shadowRoot?.querySelector("#body") ||
                                  threadRenderer.shadowRoot?.querySelector("#content") ||
                                  threadRenderer;

            const buttonWrapper = document.createElement('div');
            buttonWrapper.className = 'bili-waterfall-wrapper';
            buttonWrapper.style.cssText = 'display: inline-block; margin: 4px 0; position: relative; z-index: 1000;';
            buttonWrapper.appendChild(waterfallBtn);

            if (targetContainer.shadowRoot) {
                targetContainer.shadowRoot.appendChild(buttonWrapper);
            } else {
                targetContainer.appendChild(buttonWrapper);
            }

            // 记录已附加的按钮
            this.attachedButtons.set(buttonId, {
                threadRenderer,
                buttonElement: waterfallBtn,
                timestamp: Date.now()
            });
        }

        reattachMissingButtons() {
            try {
                const commentApp = document.querySelector("#commentapp > bili-comments");
                if (!commentApp || !commentApp.shadowRoot) return;

                const threadRenderers = commentApp.shadowRoot.querySelectorAll("#feed > bili-comment-thread-renderer");
                let reattachedCount = 0;
                let checkedCount = 0;

                // 清理过期的按钮记录（超过1分钟的记录）
                const now = Date.now();
                for (const [buttonId, buttonInfo] of this.attachedButtons.entries()) {
                    if (now - buttonInfo.timestamp > 60000) {
                        this.attachedButtons.delete(buttonId);
                    }
                }

                threadRenderers.forEach((threadRenderer) => {
                    if (!threadRenderer.shadowRoot) return;

                    const repliesRenderer = threadRenderer.shadowRoot.querySelector("#replies > bili-comment-replies-renderer");
                    if (!repliesRenderer || !repliesRenderer.shadowRoot) return;

                    const viewMoreButton = repliesRenderer.shadowRoot.querySelector("#view-more > bili-text-button");
                    if (!viewMoreButton || !viewMoreButton.shadowRoot) return;

                    const button = viewMoreButton.shadowRoot.querySelector("button");
                    if (!button || !this.viewMoreButtons.has(button)) return;

                    checkedCount++;

                    // 使用增强的检测方法检查是否已有瀑布流按钮
                    const existingButton = this.findWaterfallButton(threadRenderer);
                    if (!existingButton) {
                        const commentInfo = this.extractCommentInfo(viewMoreButton, threadRenderer);
                        if (commentInfo) {
                            // 生成按钮ID检查是否已经处理过
                            const buttonId = this.generateButtonId(threadRenderer, commentInfo);
                            if (!this.attachedButtons.has(buttonId)) {
                                this.addWaterfallButton(threadRenderer, commentInfo);
                                reattachedCount++;
                            }
                        }
                    }
                });

                // 只在实际重新附加了按钮时才输出日志
                if (reattachedCount > 0) {
                    Utils.log('info', `重新附加了 ${reattachedCount} 个瀑布流按钮 (检查了 ${checkedCount} 个评论)`);
                }

            } catch (error) {
                Utils.log('error', '重新附加按钮时出错:', error);
            }
        }

        extractCommentInfo(container, threadRenderer) {
            try {
                // 提取回复数量
                const containerText = container.textContent || '';
                const replyCountMatch = containerText.match(/(\d+)\s*条回复/) ||
                                      containerText.match(/共\s*(\d+)\s*条/) ||
                                      containerText.match(/(\d+)\s*回复/);
                const replyCount = replyCountMatch ? parseInt(replyCountMatch[1], 10) : 0;

                // 提取评论ID
                const rootId = this.extractCommentId(threadRenderer);
                const oid = this.extractVideoId();

                if (!rootId || !oid) {
                    return null;
                }

                return {
                    rootId,
                    oid,
                    replyCount,
                    container,
                    commentElement: threadRenderer
                };
            } catch (error) {
                Utils.log('error', '提取评论信息失败', error);
                return null;
            }
        }

        extractCommentId(threadRenderer) {
            // 从__data对象获取
            if (threadRenderer.__data && threadRenderer.__data.rpid) {
                return threadRenderer.__data.rpid.toString();
            }

            // 从data属性获取
            if (threadRenderer.data && threadRenderer.data.rpid) {
                return threadRenderer.data.rpid.toString();
            }

            // 从Shadow DOM中的commentRenderer获取
            if (threadRenderer.shadowRoot) {
                const commentRenderer = threadRenderer.shadowRoot.querySelector('bili-comment-renderer');
                if (commentRenderer) {
                    if (commentRenderer.__data && commentRenderer.__data.rpid) {
                        return commentRenderer.__data.rpid.toString();
                    }
                    if (commentRenderer.data && commentRenderer.data.rpid) {
                        return commentRenderer.data.rpid.toString();
                    }
                }
            }

            // 从属性获取
            const rpid = threadRenderer.getAttribute('data-rpid') ||
                        threadRenderer.getAttribute('rpid') ||
                        threadRenderer.getAttribute('data-id');

            return rpid || null;
        }

        extractVideoId() {
            // 从URL提取
            const url = window.location.href;
            const match = url.match(/\/video\/(?:av(\d+)|BV([a-zA-Z0-9]+))/);

            if (match) {
                if (match[1]) {
                    return match[1]; // av号
                } else if (match[2]) {
                    // BV号，尝试从页面数据获取对应的aid
                    const aid = this.getAidFromPageData();
                    return aid || match[2];
                }
            }

            // 从页面数据获取
            return this.getAidFromPageData();
        }

        getAidFromPageData() {
            try {
                // 尝试多种可能的全局变量
                const sources = [
                    () => window.__INITIAL_STATE__?.videoData?.aid,
                    () => window.__initialState__?.videoData?.aid,
                    () => window.__INITIAL_STATE__?.aid,
                    () => window.__initialState__?.aid,
                    () => window.aid
                ];

                for (const source of sources) {
                    const aid = source();
                    if (aid) {
                        return aid.toString();
                    }
                }
            } catch (error) {
                Utils.log('warn', '从页面数据获取视频ID失败', error);
            }

            return null;
        }

        setViewMoreClickHandler(handler) {
            this.onViewMoreClick = handler;
        }
    }

    // 主控制器类
    class BilibiliWaterfallController {
        constructor() {
            this.domWatcher = new DOMWatcher();
            this.commentAPI = new BilibiliCommentAPI();
            this.isInitialized = false;
        }

        async initialize() {
            if (this.isInitialized) {
                Utils.log('warn', '脚本已初始化');
                return;
            }

            try {
                Utils.log('info', '开始初始化Bilibili评论瀑布流脚本');

                this.domWatcher.setViewMoreClickHandler((commentInfo) => {
                    this.handleViewMoreClick(commentInfo);
                });

                this.domWatcher.start();
                this.isInitialized = true;

                Utils.log('info', 'Bilibili评论瀑布流脚本初始化完成');

            } catch (error) {
                Utils.log('error', '脚本初始化失败', error);
                throw error;
            }
        }

        async handleViewMoreClick(commentInfo) {
            try {
                Utils.log('info', '处理瀑布流按钮点击', commentInfo);

                // 显示加载提示
                this.showLoadingIndicator();

                // 获取真实的评论回复数据
                let realReplies = [];
                let apiError = null;

                if (commentInfo.rootId && commentInfo.oid && commentInfo.rootId !== 'unknown') {
                    try {
                        realReplies = await this.commentAPI.getAllReplies(commentInfo.oid, commentInfo.rootId);
                        Utils.log('info', `成功获取 ${realReplies.length} 条真实回复数据`);
                    } catch (error) {
                        Utils.log('error', '获取真实回复数据失败:', error);
                        apiError = error;
                    }
                }

                // 创建瀑布流弹出框
                this.createWaterfallModal(commentInfo.replyCount, realReplies, apiError);

                // 隐藏加载提示
                this.hideLoadingIndicator();

            } catch (error) {
                Utils.log('error', '处理瀑布流按钮失败', error);
                this.hideLoadingIndicator();
            }
        }

        showLoadingIndicator() {
            const loading = document.createElement('div');
            loading.id = 'bili-waterfall-loading';
            loading.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: #fff;
                padding: 12px 20px;
                border-radius: 4px;
                z-index: 10001;
                font-size: 14px;
            `;
            loading.textContent = '正在加载评论...';
            document.body.appendChild(loading);
        }

        hideLoadingIndicator() {
            const loading = document.getElementById('bili-waterfall-loading');
            if (loading) {
                loading.remove();
            }
        }

        createWaterfallModal(replyCount, realReplies = [], apiError = null) {
            // 创建遮罩层
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.8);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                backdrop-filter: blur(4px);
            `;

            // 创建弹出框
            const modal = document.createElement('div');
            modal.style.cssText = `
                background: #1f1f1f;
                border: 1px solid #3a3a3a;
                border-radius: 8px;
                width: 85%;
                max-width: 900px;
                max-height: 85%;
                display: flex;
                flex-direction: column;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
                color: #e1e2e3;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            `;

            // 创建头部
            const header = document.createElement('div');
            header.style.cssText = `
                padding: 16px 20px;
                border-bottom: 1px solid #3a3a3a;
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: #2a2a2a;
                border-radius: 8px 8px 0 0;
            `;

            const title = document.createElement('h3');
            title.style.cssText = `
                margin: 0;
                font-size: 16px;
                font-weight: 500;
                color: #e1e2e3;
            `;
            title.textContent = `评论回复 (${realReplies.length || replyCount}条)`;

            const closeButton = document.createElement('button');
            closeButton.style.cssText = `
                background: none;
                border: none;
                font-size: 20px;
                cursor: pointer;
                color: #9499a0;
                padding: 4px;
                border-radius: 4px;
                transition: all 0.2s ease;
            `;
            closeButton.innerHTML = '×';
            closeButton.onclick = () => overlay.remove();

            header.appendChild(title);
            header.appendChild(closeButton);
            modal.appendChild(header);

            // 创建排序控制栏
            if (realReplies.length > 0) {
                const sortBar = document.createElement('div');
                sortBar.style.cssText = `
                    padding: 12px 20px;
                    border-bottom: 1px solid #3a3a3a;
                    background: #252525;
                    display: flex;
                    gap: 12px;
                    align-items: center;
                `;

                const sortLabel = document.createElement('span');
                sortLabel.style.cssText = 'color: #9499a0; font-size: 14px;';
                sortLabel.textContent = '排序方式：';

                const sortByHot = document.createElement('button');
                sortByHot.style.cssText = `
                    background: #00a1d6;
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 4px;
                    font-size: 12px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                `;
                sortByHot.textContent = '按热度';

                let timeAscending = false;
                const sortByTime = document.createElement('button');
                sortByTime.style.cssText = `
                    background: #3a3a3a;
                    color: #e1e2e3;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 4px;
                    font-size: 12px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                `;
                sortByTime.textContent = '按时间↓';

                sortBar.appendChild(sortLabel);
                sortBar.appendChild(sortByHot);
                sortBar.appendChild(sortByTime);
                modal.appendChild(sortBar);

                // 排序事件
                sortByHot.onclick = () => {
                    sortByHot.style.background = '#00a1d6';
                    sortByTime.style.background = '#3a3a3a';
                    this.renderComments(contentContainer, realReplies, 'hot');
                };

                sortByTime.onclick = () => {
                    timeAscending = !timeAscending;
                    sortByTime.textContent = timeAscending ? '按时间↑' : '按时间↓';
                    sortByHot.style.background = '#3a3a3a';
                    sortByTime.style.background = '#00a1d6';
                    this.renderComments(contentContainer, realReplies, timeAscending ? 'time_asc' : 'time_desc');
                };
            }

            // 创建内容区域
            const contentContainer = document.createElement('div');
            contentContainer.style.cssText = `
                flex: 1;
                overflow-y: auto;
                padding: 0;
                background: #1f1f1f;
            `;

            modal.appendChild(contentContainer);

            // 渲染评论内容
            if (realReplies.length > 0) {
                this.renderComments(contentContainer, realReplies, 'hot');
            } else if (apiError) {
                this.renderErrorMessage(contentContainer, apiError);
            } else {
                this.renderEmptyMessage(contentContainer);
            }

            overlay.appendChild(modal);
            document.body.appendChild(overlay);

            // 点击遮罩关闭
            overlay.onclick = (e) => {
                if (e.target === overlay) {
                    overlay.remove();
                }
            };

            // ESC键关闭
            const handleKeyDown = (e) => {
                if (e.key === 'Escape') {
                    overlay.remove();
                    document.removeEventListener('keydown', handleKeyDown);
                }
            };
            document.addEventListener('keydown', handleKeyDown);
        }

        renderComments(container, replies, sortType = 'hot') {
            // 排序评论
            let sortedReplies = [...replies];

            if (sortType === 'hot') {
                sortedReplies.sort((a, b) => (b.like || 0) - (a.like || 0));
            } else if (sortType === 'time_desc') {
                sortedReplies.sort((a, b) => (b.ctime || 0) - (a.ctime || 0));
            } else if (sortType === 'time_asc') {
                sortedReplies.sort((a, b) => (a.ctime || 0) - (b.ctime || 0));
            }

            container.innerHTML = '';

            if (sortedReplies.length === 0) {
                this.renderEmptyMessage(container);
                return;
            }

            sortedReplies.forEach((reply, index) => {
                const commentElement = this.createCommentElement(reply, index);
                container.appendChild(commentElement);
            });
        }

        createCommentElement(reply, index) {
            const commentDiv = document.createElement('div');
            commentDiv.style.cssText = `
                padding: 16px 20px;
                border-bottom: 1px solid #2a2a2a;
                background: ${index % 2 === 0 ? '#1f1f1f' : '#252525'};
                transition: background-color 0.2s ease;
            `;

            // 用户信息
            const userInfo = document.createElement('div');
            userInfo.style.cssText = `
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                gap: 8px;
            `;

            const avatar = document.createElement('img');
            avatar.src = reply.member?.avatar || 'https://i0.hdslb.com/bfs/face/member/noface.jpg';
            avatar.style.cssText = `
                width: 32px;
                height: 32px;
                border-radius: 50%;
                object-fit: cover;
            `;

            const userName = document.createElement('span');
            userName.style.cssText = `
                color: ${reply.member?.vip?.vipStatus ? '#fb7299' : '#61c05a'};
                font-weight: 500;
                font-size: 14px;
                cursor: pointer;
                text-decoration: none;
                transition: color 0.2s ease;
            `;
            userName.textContent = reply.member?.uname || '匿名用户';

            // 用户名点击跳转
            userName.onclick = (e) => {
                e.preventDefault();
                e.stopPropagation();
                if (reply.member?.mid) {
                    window.open(`https://space.bilibili.com/${reply.member.mid}`, '_blank');
                }
            };

            const timeSpan = document.createElement('span');
            timeSpan.style.cssText = `
                color: #9499a0;
                font-size: 12px;
                margin-left: auto;
            `;
            timeSpan.textContent = Utils.formatTime(reply.ctime);
            timeSpan.title = Utils.formatDetailedTime(reply.ctime);

            userInfo.appendChild(avatar);
            userInfo.appendChild(userName);
            userInfo.appendChild(timeSpan);

            // 评论内容
            const contentDiv = document.createElement('div');
            contentDiv.style.cssText = `
                color: #e1e2e3;
                font-size: 14px;
                line-height: 1.6;
                margin-bottom: 8px;
                text-align: left;
                word-wrap: break-word;
                white-space: pre-wrap;
            `;

            const processedContent = Utils.processCommentContent(reply.content?.message || '');
            contentDiv.innerHTML = processedContent;

            // 添加视频链接悬停效果
            Utils.addVideoLinkHoverEffects(contentDiv);

            // 点赞信息
            const likeInfo = document.createElement('div');
            likeInfo.style.cssText = `
                display: flex;
                align-items: center;
                gap: 4px;
                color: #9499a0;
                font-size: 12px;
            `;

            if (reply.like > 0) {
                likeInfo.innerHTML = `
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                    </svg>
                    <span>${Utils.formatNumber(reply.like)}</span>
                `;
            }

            commentDiv.appendChild(userInfo);
            commentDiv.appendChild(contentDiv);
            commentDiv.appendChild(likeInfo);

            // 悬停效果
            commentDiv.onmouseover = () => {
                commentDiv.style.background = '#2a2a2a';
            };
            commentDiv.onmouseout = () => {
                commentDiv.style.background = index % 2 === 0 ? '#1f1f1f' : '#252525';
            };

            return commentDiv;
        }

        renderErrorMessage(container) {
            container.innerHTML = `
                <div style="
                    padding: 40px 20px;
                    text-align: center;
                    color: #9499a0;
                    font-size: 14px;
                ">
                    <div style="font-size: 48px; margin-bottom: 16px;">⚠️</div>
                    <div>获取评论数据失败</div>
                    <div style="margin-top: 8px; font-size: 12px;">请稍后重试或检查网络连接</div>
                </div>
            `;
        }

        renderEmptyMessage(container) {
            container.innerHTML = `
                <div style="
                    padding: 40px 20px;
                    text-align: center;
                    color: #9499a0;
                    font-size: 14px;
                ">
                    <div style="font-size: 48px; margin-bottom: 16px;">💬</div>
                    <div>暂无评论回复</div>
                </div>
            `;
        }

        getStatus() {
            return {
                initialized: this.isInitialized,
                buttonsProcessed: this.domWatcher.viewMoreButtons.size,
                cacheSize: this.commentAPI.cache.size
            };
        }

        destroy() {
            this.domWatcher.stop();
            this.commentAPI.clearCache();
            this.isInitialized = false;
        }
    }

    // 初始化脚本
    function initializeScript() {
        // 等待页面加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeScript);
            return;
        }

        // 延迟启动，确保B站页面完全加载
        setTimeout(() => {
            try {
                const controller = new BilibiliWaterfallController();

                controller.initialize().then(() => {
                    Utils.log('info', '脚本初始化成功');

                    // 设置全局接口
                    window.bilibiliWaterfall = {
                        controller: controller,
                        getStatus: () => controller.getStatus(),
                        destroy: () => controller.destroy()
                    };

                    Utils.log('info', '全局接口已添加: window.bilibiliWaterfall');

                }).catch(error => {
                    Utils.log('error', '脚本初始化失败', error);
                });

            } catch (error) {
                Utils.log('error', '脚本启动失败', error);
            }
        }, 3000);
    }

    // 启动脚本
    Utils.log('info', 'Bilibili评论瀑布流脚本 v2.4.0 已加载 - 优化按钮检测逻辑');
    initializeScript();

})();
